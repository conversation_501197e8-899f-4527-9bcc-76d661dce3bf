"""
信息获取分类模块
充分利用浏览器分类的各种接口，获取网页源码并解析内容
支持OCR识别、图像分析等高级功能
"""

from .novel_catalog_kit import NovelCatalogKit

# 可选模块，如果不存在则跳过
try:
    from .ocr_processor import OCRProcessor
    OCR_AVAILABLE = True
except ImportError:
    OCRProcessor = None
    OCR_AVAILABLE = False

try:
    from .data_extractor import DataExtractor
    DATA_EXTRACTOR_AVAILABLE = True
except ImportError:
    DataExtractor = None
    DATA_EXTRACTOR_AVAILABLE = False

try:
    from .novel_scraper import NovelScraper
    NOVEL_SCRAPER_AVAILABLE = True
except ImportError:
    NovelScraper = None
    NOVEL_SCRAPER_AVAILABLE = False

try:
    from .image_analyzer import ImageAnalyzer
    IMAGE_ANALYZER_AVAILABLE = True
except ImportError:
    ImageAnalyzer = None
    IMAGE_ANALYZER_AVAILABLE = False

__all__ = ['NovelCatalogKit']

# 只导出可用的模块
if OCR_AVAILABLE:
    __all__.append('OCRProcessor')
if DATA_EXTRACTOR_AVAILABLE:
    __all__.append('DataExtractor')
if NOVEL_SCRAPER_AVAILABLE:
    __all__.append('NovelScraper')
if IMAGE_ANALYZER_AVAILABLE:
    __all__.append('ImageAnalyzer')

__version__ = "4.0.0"
__author__ = "起点爬虫团队"


class ExtractorInterface:
    """信息获取统一接口"""
    
    def __init__(self, browser_interface=None):
        """初始化信息获取接口"""
        self.browser = browser_interface
        self.content_parser = ContentParser()

        # 可选模块初始化
        self.ocr_processor = OCRProcessor() if OCR_AVAILABLE else None
        self.data_extractor = DataExtractor() if DATA_EXTRACTOR_AVAILABLE else None
        self.novel_scraper = NovelScraper(browser_interface) if NOVEL_SCRAPER_AVAILABLE else None
        self.image_analyzer = ImageAnalyzer() if IMAGE_ANALYZER_AVAILABLE else None
    
    def parse_page(self, url: str) -> dict:
        """解析页面内容"""
        if not self.browser:
            return {"success": False, "error": "浏览器接口未初始化"}
        
        # 导航到页面
        nav_result = self.browser.navigate(url)
        if not nav_result.get("success"):
            return nav_result
        
        # 获取页面源码
        html = self.browser.get_source()
        if not html:
            return {"success": False, "error": "无法获取页面源码"}
        
        # 解析内容
        parsed_data = self.content_parser.parse_html(html)
        
        return {
            "success": True,
            "url": url,
            "title": nav_result.get("title", ""),
            "content": parsed_data
        }
    
    def extract_text(self, selector: str) -> str:
        """提取指定元素的文本"""
        if self.browser:
            return self.browser.get_text(selector)
        return ""
    
    def extract_links(self, selector: str = "a") -> list:
        """提取链接"""
        if not self.browser:
            return []
        
        html = self.browser.get_source()
        return self.content_parser.extract_links(html)
    
    def extract_data(self, selectors: dict) -> dict:
        """根据选择器字典提取数据"""
        if not self.browser:
            return {}
        
        result = {}
        for key, selector in selectors.items():
            result[key] = self.browser.get_text(selector)
        
        return result
    
    def ocr_element(self, selector: str) -> dict:
        """对元素进行OCR识别"""
        if not self.browser:
            return {"success": False, "error": "浏览器接口未初始化"}
        
        # 截取元素
        screenshot_path = self.browser.screenshot_element(selector)
        if not screenshot_path:
            return {"success": False, "error": "元素截图失败"}
        
        # OCR识别
        return self.ocr_processor.recognize_text(screenshot_path)
    
    def ocr_image(self, image_path: str) -> dict:
        """对图片进行OCR识别"""
        return self.ocr_processor.recognize_text(image_path)
    
    def extract_novel(self, novel_url: str) -> dict:
        """提取小说信息"""
        return self.novel_scraper.get_novel_detail(novel_url)
    
    def extract_chapters(self, novel_url: str) -> list:
        """提取章节列表"""
        return self.novel_scraper.get_chapter_list(novel_url)
    
    def extract_content(self, chapter_url: str) -> dict:
        """提取章节内容"""
        return self.novel_scraper.download_chapter(chapter_url)
    
    def analyze_image(self, image_path: str) -> dict:
        """分析图像"""
        return self.image_analyzer.analyze_captcha(image_path)