"""
起点爬虫主启动程序
提供交互式界面进行小说下载和账号管理
"""

import sys
import os
import time
import re
from pathlib import Path

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from browser import BrowserManager
from extractor import NovelCatalogKit
from storage import StorageManager

class QidianCrawler:
    """起点爬虫主类"""
    
    def __init__(self):
        """初始化爬虫"""
        self.browser_manager = BrowserManager()
        self.novel_catalog_kit = NovelCatalogKit()  # 小说目录组件
        self.storage_manager = StorageManager()     # 存储管理组件
        self.page = None
        self.current_profile = "qidian_user"
        self.is_logged_in = False
        
        # 确保必要的目录存在
        Path("novels").mkdir(exist_ok=True)
        Path("profiles").mkdir(exist_ok=True)
        Path("screenshots").mkdir(exist_ok=True)
    
    def start(self):
        """启动爬虫主程序"""
        print("🚀 起点小说爬虫 v4.0")
        print("=" * 50)
        
        try:
            # 创建浏览器并访问起点官网
            print("正在启动浏览器...")
            self.page = self.browser_manager.create_browser(profile_name=self.current_profile)
            
            # 尝试加载已保存的配置文件
            if self.current_profile in self.browser_manager.list_profiles():
                print("发现已保存的用户配置，正在加载...")
                self.browser_manager.load_browser_profile(self.current_profile)
                self.is_logged_in = True
            
            # 访问起点官网
            print("正在访问起点中文网...")
            result = self.browser_manager.navigate_to("https://www.qidian.com")
            
            if result.get("success"):
                print(f"✅ 成功访问起点中文网")
                self.show_main_menu()
            else:
                print(f"❌ 访问失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"❌ 启动失败: {str(e)}")
        finally:
            if self.browser_manager:
                self.browser_manager.close_browser()
    
    def show_main_menu(self):
        """显示主菜单"""
        while True:
            print("\n" + "=" * 50)
            print("📚 起点小说爬虫主菜单")
            print("=" * 50)
            print("1. 下载小说")
            print("2. 登录账号")
            print("0. 退出程序")
            print("-" * 50)
            
            choice = input("请选择操作 (0-2): ").strip()
            
            if choice == "1":
                self.download_novels()
            elif choice == "2":
                self.login_account()
            elif choice == "0":
                print("👋 感谢使用，再见！")
                break
            else:
                print("❌ 无效选择，请重新输入")
    
    def download_novels(self):
        """下载小说功能"""
        print("\n📖 小说下载功能")
        print("=" * 40)
        
        # 输入小说ID
        novel_ids_input = input("请输入小说ID（多个ID用逗号分隔）: ").strip()
        if not novel_ids_input:
            print("❌ 未输入小说ID")
            return
        
        # 解析小说ID
        novel_ids = [id.strip() for id in novel_ids_input.split(",") if id.strip()]
        if not novel_ids:
            print("❌ 无效的小说ID格式")
            return
        
        print(f"📋 准备下载 {len(novel_ids)} 本小说: {', '.join(novel_ids)}")
        
        # 逐个处理小说
        for i, novel_id in enumerate(novel_ids, 1):
            print(f"\n📚 正在处理第 {i}/{len(novel_ids)} 本小说 (ID: {novel_id})")
            try:
                self.download_single_novel(novel_id)
            except Exception as e:
                print(f"❌ 下载小说 {novel_id} 失败: {str(e)}")
                continue
        
        print(f"\n✅ 批量下载完成！共处理 {len(novel_ids)} 本小说")

    def download_single_novel(self, novel_id):
        """下载单本小说"""
        # 构建小说URL
        novel_url = f"https://book.qidian.com/info/{novel_id}"

        print(f"🔗 访问小说页面: {novel_url}")
        result = self.browser_manager.navigate_to(novel_url)

        if not result.get("success"):
            print(f"❌ 无法访问小说页面: {result.get('error', '未知错误')}")
            return

        time.sleep(2)  # 等待页面加载

        # 获取小说信息
        print("📋 正在获取小说信息...")
        novel_info = self.get_novel_info()

        if not novel_info:
            print("❌ 无法获取小说信息")
            return

        print(f"📖 小说名称: {novel_info.get('title', '未知')}")
        print(f"✍️ 作者: {novel_info.get('author', '未知')}")
        print(f"📊 状态: {novel_info.get('status', '未知')}")

        # 获取章节列表
        print("📑 正在获取章节列表...")
        catalog_url = f"https://book.qidian.com/info/{novel_id}#Catalog"
        self.browser_manager.navigate_to(catalog_url)
        time.sleep(3)  # 等待章节列表加载

        chapters = self.get_chapter_list()
        if not chapters:
            print("❌ 无法获取章节列表")
            return

        print(f"📚 共找到 {len(chapters)} 个章节")

        # 输入章节范围
        start_chapter, end_chapter = self.get_chapter_range(len(chapters))
        if start_chapter is None or end_chapter is None:
            return

        print(f"📥 准备下载章节 {start_chapter} 到 {end_chapter}")

        # 下载指定范围的章节
        selected_chapters = chapters[start_chapter-1:end_chapter]
        self.download_chapters(novel_info, selected_chapters, start_chapter)

    def get_novel_info(self):
        """获取小说基本信息"""
        try:
            source = self.browser_manager.get_page_source()
            self.content_parser.parse_html(source)

            # 提取小说信息
            title_element = self.page.ele(".book-info h1")
            author_element = self.page.ele(".book-info .writer")
            status_element = self.page.ele(".tag-wrap .tag")

            novel_info = {
                'title': title_element.text if title_element else "未知小说",
                'author': author_element.text if author_element else "未知作者",
                'status': status_element.text if status_element else "未知状态",
                'url': self.browser_manager.page.url
            }

            return novel_info

        except Exception as e:
            print(f"⚠️ 获取小说信息时出错: {str(e)}")
            return None

    def get_chapter_list(self):
        """获取章节列表"""
        try:
            # 等待章节列表加载
            time.sleep(2)

            # 查找章节链接
            chapter_elements = self.page.eles(".volume-wrap .cf a")

            chapters = []
            for element in chapter_elements:
                chapter_title = element.text.strip()
                chapter_url = element.attr("href")

                if chapter_title and chapter_url:
                    # 确保URL是完整的
                    if chapter_url.startswith("/"):
                        chapter_url = "https://read.qidian.com" + chapter_url

                    chapters.append({
                        'title': chapter_title,
                        'url': chapter_url
                    })

            return chapters

        except Exception as e:
            print(f"⚠️ 获取章节列表时出错: {str(e)}")
            return []

    def get_chapter_range(self, total_chapters):
        """获取用户指定的章节范围"""
        print(f"\n📊 小说共有 {total_chapters} 个章节")

        try:
            start_input = input(f"请输入起始章节 (1-{total_chapters}): ").strip()
            if not start_input:
                print("❌ 未输入起始章节")
                return None, None

            start_chapter = int(start_input)
            if start_chapter < 1 or start_chapter > total_chapters:
                print(f"❌ 起始章节超出范围 (1-{total_chapters})")
                return None, None

            end_input = input(f"请输入结束章节 ({start_chapter}-{total_chapters}): ").strip()
            if not end_input:
                print("❌ 未输入结束章节")
                return None, None

            end_chapter = int(end_input)
            if end_chapter < start_chapter or end_chapter > total_chapters:
                print(f"❌ 结束章节超出范围 ({start_chapter}-{total_chapters})")
                return None, None

            return start_chapter, end_chapter

        except ValueError:
            print("❌ 请输入有效的数字")
            return None, None

    def download_chapters(self, novel_info, chapters, start_num):
        """下载章节内容"""
        novel_title = novel_info.get('title', 'unknown')
        safe_title = re.sub(r'[<>:"/\\|?*]', '_', novel_title)  # 清理文件名

        # 创建小说目录
        novel_dir = Path("novels") / safe_title
        novel_dir.mkdir(exist_ok=True)

        print(f"📁 小说将保存到: {novel_dir}")

        success_count = 0
        total_count = len(chapters)

        for i, chapter in enumerate(chapters):
            chapter_num = start_num + i
            print(f"\n📄 下载第 {chapter_num} 章: {chapter['title']}")

            try:
                # 访问章节页面
                result = self.browser_manager.navigate_to(chapter['url'])
                if not result.get("success"):
                    print(f"❌ 无法访问章节: {result.get('error', '未知错误')}")
                    continue

                time.sleep(2)  # 等待页面加载

                # 获取章节内容
                content = self.get_chapter_content()
                if not content:
                    print("❌ 无法获取章节内容")
                    continue

                # 保存章节
                chapter_file = novel_dir / f"第{chapter_num:04d}章_{re.sub(r'[<>:\"/\\|?*]', '_', chapter['title'])}.txt"

                with open(chapter_file, 'w', encoding='utf-8') as f:
                    f.write(f"第{chapter_num}章 {chapter['title']}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(content)
                    f.write(f"\n\n下载时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

                success_count += 1
                print(f"✅ 第 {chapter_num} 章下载完成 ({success_count}/{total_count})")

                # 添加延迟避免请求过快
                time.sleep(1)

            except Exception as e:
                print(f"❌ 下载第 {chapter_num} 章失败: {str(e)}")
                continue

        print(f"\n🎉 章节下载完成！成功: {success_count}/{total_count}")

        # 生成合并文件
        if success_count > 0:
            self.create_merged_file(novel_dir, novel_info, success_count)

    def get_chapter_content(self):
        """获取章节正文内容"""
        try:
            # 查找章节内容元素
            content_element = self.page.ele(".read-content")

            if content_element:
                # 获取纯文本内容
                content = content_element.text
                # 清理内容
                content = re.sub(r'\n\s*\n', '\n\n', content)  # 规范化换行
                content = content.strip()
                return content
            else:
                print("⚠️ 未找到章节内容元素")
                return None

        except Exception as e:
            print(f"⚠️ 获取章节内容时出错: {str(e)}")
            return None

    def create_merged_file(self, novel_dir, novel_info, chapter_count):
        """创建合并的完整小说文件"""
        try:
            merged_file = novel_dir / f"{novel_dir.name}_完整版.txt"

            with open(merged_file, 'w', encoding='utf-8') as f:
                # 写入小说信息
                f.write(f"{novel_info.get('title', '未知小说')}\n")
                f.write(f"作者: {novel_info.get('author', '未知作者')}\n")
                f.write(f"状态: {novel_info.get('status', '未知状态')}\n")
                f.write(f"下载时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"章节数量: {chapter_count}\n")
                f.write("=" * 80 + "\n\n")

                # 合并所有章节文件
                for chapter_file in sorted(novel_dir.glob("第*.txt")):
                    if chapter_file.name.endswith("_完整版.txt"):
                        continue

                    with open(chapter_file, 'r', encoding='utf-8') as cf:
                        f.write(cf.read())
                        f.write("\n\n" + "=" * 50 + "\n\n")

            print(f"📚 完整版小说已生成: {merged_file}")

        except Exception as e:
            print(f"⚠️ 生成完整版文件时出错: {str(e)}")

    def login_account(self):
        """登录账号功能"""
        print("\n🔐 账号登录功能")
        print("=" * 40)
        
        try:
            # 跳转到登录页面
            print("🔗 正在跳转到登录页面...")
            login_url = "https://passport.qidian.com/login"
            result = self.browser_manager.navigate_to(login_url)
            
            if not result.get("success"):
                print(f"❌ 无法访问登录页面: {result.get('error', '未知错误')}")
                return
            
            print("✅ 已跳转到登录页面")
            print("📝 请在浏览器中完成登录操作")
            print("⚠️ 登录完成后，请在此终端按 Enter 键确认...")
            
            # 等待用户确认登录完成
            input()
            
            print("🔍 正在检测登录状态...")
            
            # 检查登录状态
            self.browser_manager.navigate_to("https://www.qidian.com")
            time.sleep(2)
            
            # 检查是否有用户信息元素
            user_element = self.page.ele(".nav-user-info")
            
            if user_element:
                print("✅ 登录成功！")
                
                # 获取用户信息
                username = "用户"
                try:
                    username_element = self.page.ele(".nav-user-info .name")
                    if username_element:
                        username = username_element.text
                except:
                    pass
                
                print(f"👤 欢迎，{username}！")
                
                # 保存登录状态
                print("💾 正在保存登录状态...")
                save_result = self.browser_manager.save_browser_profile(self.current_profile)
                
                if save_result:
                    print("✅ 登录状态保存成功")
                    self.is_logged_in = True
                    
                    # 获取并保存指纹信息
                    fingerprint = self.browser_manager.get_browser_fingerprint()
                    print("🔐 指纹信息已记录，下次使用将自动应用")
                    
                else:
                    print("⚠️ 登录状态保存失败")
                
                # 自动退出登录（可选）
                logout_choice = input("\n是否自动退出登录以测试下次自动登录？(y/N): ").strip().lower()
                if logout_choice == 'y':
                    self.logout_account()
                
            else:
                print("❌ 未检测到登录状态，请确认是否已完成登录")
                
        except Exception as e:
            print(f"❌ 登录过程中出错: {str(e)}")
    
    def logout_account(self):
        """退出登录"""
        try:
            print("🚪 正在退出登录...")
            
            # 尝试找到退出登录的链接或按钮
            logout_element = self.page.ele("a[href*='logout']")
            if logout_element:
                logout_element.click()
                time.sleep(2)
                print("✅ 已退出登录")
            else:
                # 如果找不到退出按钮，清除cookies
                self.page.run_js("document.cookie.split(';').forEach(function(c) { document.cookie = c.replace(/^ +/, '').replace(/=.*/, '=;expires=' + new Date().toUTCString() + ';path=/'); });")
                print("✅ 已清除登录信息")
            
            self.is_logged_in = False
            
        except Exception as e:
            print(f"⚠️ 退出登录时出错: {str(e)}")

    def get_novel_catalog(self, novel_url: str, filename: str = None):
        """
        获取小说目录信息

        Args:
            novel_url: 小说URL
            filename: 保存文件名（可选）

        Returns:
            dict: 小说信息字典
        """
        try:
            print(f"📚 正在获取小说目录: {novel_url}")

            # 使用目录组件获取小说信息
            result = self.novel_catalog_kit.get_catalog_sync(novel_url, browser=self.page, filename=filename)

            if result and 'book_name' in result:
                print(f"✅ 小说信息获取成功: {result['book_name']}")
                print(f"📖 作者: {result.get('author', 'Unknown')}")
                print(f"📚 章节数: {len(result.get('chapters', []))}")
                return result
            else:
                print("❌ 小说信息获取失败")
                return None

        except Exception as e:
            print(f"❌ 获取小说目录失败: {str(e)}")
            return None

    def list_stored_novels(self):
        """列出已存储的小说"""
        try:
            novels = self.storage_manager.get_novels_list()

            if novels:
                print(f"📚 已存储的小说 ({len(novels)} 本):")
                print("-" * 50)
                for i, novel in enumerate(novels, 1):
                    print(f"{i:2d}. {novel['book_name']} - {novel['author']}")
                    print(f"     章节: {novel['chapter_count']} | 更新: {novel['update_time']}")
                return novels
            else:
                print("📚 暂无已存储的小说")
                return []

        except Exception as e:
            print(f"❌ 获取小说列表失败: {str(e)}")
            return []

    def search_novels(self, keyword: str):
        """搜索小说"""
        try:
            results = self.novel_catalog_kit.search_novels(keyword)

            if results:
                print(f"🔍 搜索结果 ('{keyword}' - {len(results)} 个结果):")
                print("-" * 50)
                for i, novel in enumerate(results, 1):
                    print(f"{i:2d}. {novel['book_name']} - {novel['author']}")
                return results
            else:
                print(f"🔍 未找到包含 '{keyword}' 的小说")
                return []

        except Exception as e:
            print(f"❌ 搜索失败: {str(e)}")
            return []

    def get_storage_statistics(self):
        """获取存储统计信息"""
        try:
            stats = self.storage_manager.get_storage_stats()

            print("📊 存储统计信息:")
            print("-" * 30)
            print(f"小说数量: {stats.get('novel_count', 0)} 本")
            print(f"章节数量: {stats.get('chapter_count', 0)} 章")
            print(f"总字数: {stats.get('total_words', 0):,} 字")
            print(f"存储大小: {stats.get('storage_size', 0):,} bytes")
            print(f"存储目录: {stats.get('storage_dir', 'Unknown')}")

            return stats

        except Exception as e:
            print(f"❌ 获取统计信息失败: {str(e)}")
            return {}

def main():
    """主函数"""
    try:
        crawler = QidianCrawler()
        crawler.start()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，程序退出")
    except Exception as e:
        print(f"\n❌ 程序异常: {str(e)}")

if __name__ == "__main__":
    main()
