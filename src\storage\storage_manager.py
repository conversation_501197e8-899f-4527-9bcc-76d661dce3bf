"""
存储管理器
负责小说数据的JSON格式存储和管理
基于原有storage_kit.py的功能，适配项目结构
"""

import os
import json
import re
from typing import Optional, Dict, List
from pathlib import Path
import logging

logger = logging.getLogger("storage")


class StorageManager:
    """
    存储管理器 - 负责小说数据的JSON格式存储
    保持与原有storage_kit.py相同的功能和接口
    """
    
    def __init__(self, base_dir=None):
        """
        初始化存储管理器
        
        Args:
            base_dir: 存储基础目录，默认为项目根目录下的novels文件夹
        """
        if base_dir is None:
            # 使用项目根目录下的novels文件夹
            project_root = Path(__file__).parent.parent.parent
            base_dir = project_root / 'novels'
        
        self.base_dir = Path(base_dir)
        # 确保目录存在
        self.base_dir.mkdir(exist_ok=True)
        
        logger.info(f"存储管理器初始化完成，存储目录: {self.base_dir}")

    def _sanitize_filename(self, name: str) -> str:
        """清理文件名，只保留安全字符"""
        return re.sub(r'[^\w\u4e00-\u9fa5]', '_', name)

    def create_novel_file(self, book_name: str, filename: Optional[str] = None) -> str:
        """
        创建一个初始json文件，只包含book_name等基础字段。
        返回实际创建的文件名。
        """
        if not filename:
            safe_name = self._sanitize_filename(book_name)
            filename = f"{safe_name}.json"
        
        file_path = self.base_dir / filename
        data = {
            "book_name": book_name
        }
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"创建小说文件成功: {filename}")
            return filename
        except Exception as e:
            logger.error(f"创建小说文件失败: {str(e)}")
            return ""

    def import_novel_data(
        self,
        novel_id: str,
        book_name: str,
        cover_img: str,
        author: str,
        update_time: str,
        latest_chapter: str,
        taglist: list,
        categorytags: list,  # 修正为list类型，与原始实际运行行为一致
        intro: str,
        work_intro: str,
        chapters: list,
        filename: Optional[str] = None
    ) -> None:  # 修正返回类型，与原始storage_kit.py一致
        """
        向指定json文件写入完整数据（覆盖写入）。
        
        Args:
            filename: 文件名（如 '重启人生.json'），如果未传则自动用book_name生成
            其他参数参考小说数据结构
            
        Returns:
            bool: 是否保存成功
        """
        try:
            # 自动将章节index重排为从1开始递增
            for i, chapter in enumerate(chapters, start=1):
                chapter['index'] = i
            
            data = {
                "novel_id": novel_id,
                "book_name": book_name,
                "cover_img": cover_img,
                "author": author,
                "update_time": update_time,
                "latest_chapter": latest_chapter,
                "taglist": taglist,
                "categorytags": categorytags,
                "intro": intro,
                "work_intro": work_intro,
                "chapters": chapters
            }
            
            # 移除额外数据处理，严格按照原始storage_kit.py格式
            
            if not filename:
                safe_name = self._sanitize_filename(book_name)
                filename = f"{safe_name}.json"
            
            file_path = self.base_dir / filename
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"小说数据保存成功: {book_name} -> {filename}")
            return True
            
        except Exception as e:
            logger.error(f"保存小说数据失败: {str(e)}")
            return False

    def save_chapter(self, filename: str, index: int, content: str) -> bool:
        """
        保存章节内容到指定文件
        
        Args:
            filename: 小说文件名
            index: 章节索引
            content: 章节内容
            
        Returns:
            bool: 是否保存成功
        """
        try:
            file_path = self.base_dir / filename
            
            # 如果文件不存在，创建基础结构
            if not file_path.exists():
                data = {
                    "book_name": Path(filename).stem,
                    "chapters": []
                }
            else:
                # 读取现有数据
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if "chapters" not in data:
                    data["chapters"] = []
            
            # 查找或创建章节
            chapter_found = False
            for chapter in data["chapters"]:
                if chapter.get("index") == index:
                    chapter["content"] = content
                    chapter_found = True
                    break
            
            if not chapter_found:
                # 添加新章节
                new_chapter = {
                    "index": index,
                    "content": content,
                    "title": f"第{index}章",  # 修正为title字段，与原始storage_kit.py一致
                    "url": ""
                }
                data["chapters"].append(new_chapter)
                # 按index排序
                data["chapters"].sort(key=lambda x: x.get("index", 0))
            
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"章节内容保存成功: {filename} - 第{index}章")
            return True
            
        except Exception as e:
            logger.error(f"保存章节失败: {str(e)}")
            return False

    def load_novel_file(self, filename: str) -> Optional[Dict]:
        """
        加载小说文件数据

        Args:
            filename: 文件名

        Returns:
            Dict: 小说数据，失败返回None
        """
        try:
            file_path = self.base_dir / filename
            if not file_path.exists():
                logger.warning(f"小说文件不存在: {filename}")
                return None

            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            logger.debug(f"小说文件加载成功: {filename}")
            return data

        except Exception as e:
            logger.error(f"加载小说文件失败: {str(e)}")
            return None

    def get_novels_list(self) -> List[Dict]:
        """
        获取所有小说的列表信息

        Returns:
            List[Dict]: 小说信息列表
        """
        novels = []
        try:
            for json_file in self.base_dir.glob("*.json"):
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    # 提取基本信息
                    novel_info = {
                        'filename': json_file.name,
                        'book_name': data.get('book_name', ''),
                        'author': data.get('author', ''),
                        'novel_id': data.get('novel_id', ''),
                        'chapter_count': len(data.get('chapters', [])),
                        'latest_chapter': data.get('latest_chapter', ''),
                        'update_time': data.get('update_time', '')
                    }
                    novels.append(novel_info)

                except Exception as e:
                    logger.warning(f"读取小说文件失败: {json_file.name} - {str(e)}")
                    continue

            # 按更新时间排序
            novels.sort(key=lambda x: x.get('update_time', ''), reverse=True)
            logger.info(f"获取小说列表成功: {len(novels)} 本")
            return novels

        except Exception as e:
            logger.error(f"获取小说列表失败: {str(e)}")
            return []

    def get_novel_info(self, filename: str) -> Optional[Dict]:
        """
        获取指定小说的详细信息

        Args:
            filename: 文件名

        Returns:
            Dict: 小说详细信息，失败返回None
        """
        return self.load_novel_file(filename)

    def search_novels(self, keyword: str) -> List[Dict]:
        """
        搜索小说

        Args:
            keyword: 搜索关键词

        Returns:
            List[Dict]: 搜索结果列表
        """
        results = []
        try:
            all_novels = self.get_novels_list()

            for novel in all_novels:
                # 在书名、作者、简介中搜索
                novel_data = self.load_novel_file(novel['filename'])
                if not novel_data:
                    continue

                search_text = f"{novel_data.get('book_name', '')} {novel_data.get('author', '')} {novel_data.get('intro', '')}"

                if keyword.lower() in search_text.lower():
                    results.append(novel)

            logger.info(f"搜索完成: 关键词'{keyword}' 找到 {len(results)} 个结果")
            return results

        except Exception as e:
            logger.error(f"搜索小说失败: {str(e)}")
            return []

    def delete_novel(self, filename: str) -> bool:
        """
        删除小说文件

        Args:
            filename: 文件名

        Returns:
            bool: 是否删除成功
        """
        try:
            file_path = self.base_dir / filename
            if file_path.exists():
                file_path.unlink()
                logger.info(f"小说文件删除成功: {filename}")
                return True
            else:
                logger.warning(f"小说文件不存在: {filename}")
                return False

        except Exception as e:
            logger.error(f"删除小说文件失败: {str(e)}")
            return False

    def get_storage_stats(self) -> Dict:
        """
        获取存储统计信息

        Returns:
            Dict: 统计信息
        """
        try:
            novels = self.get_novels_list()
            total_chapters = sum(novel.get('chapter_count', 0) for novel in novels)

            # 计算总字数
            total_words = 0
            for novel in novels:
                novel_data = self.load_novel_file(novel['filename'])
                if novel_data and 'chapters' in novel_data:
                    for chapter in novel_data['chapters']:
                        content = chapter.get('content', '')
                        total_words += len(content)

            # 计算存储空间
            total_size = 0
            for json_file in self.base_dir.glob("*.json"):
                total_size += json_file.stat().st_size

            stats = {
                'novel_count': len(novels),
                'chapter_count': total_chapters,
                'total_words': total_words,
                'storage_size': total_size,
                'storage_dir': str(self.base_dir),
                'recent_novels': novels[:5]  # 最近的5本小说
            }

            logger.info(f"存储统计: {stats['novel_count']}本小说, {stats['chapter_count']}章节, {stats['total_words']}字")
            return stats

        except Exception as e:
            logger.error(f"获取存储统计失败: {str(e)}")
            return {}
