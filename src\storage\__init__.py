"""
数据存储分类模块
负责数据的持久化存储，支持JSON格式存储
提供小说信息和章节内容的存储管理功能
"""

from .storage_manager import StorageManager

__all__ = [
    'StorageManager'
]

__version__ = "4.0.0"
__author__ = "起点爬虫团队"


class StorageInterface:
    """数据存储统一接口"""
    
    def __init__(self, base_dir=None):
        """初始化存储接口"""
        self.storage_manager = StorageManager(base_dir=base_dir)
    
    def save_novel_data(self, novel_data: dict, filename: str = None) -> bool:
        """保存小说数据"""
        return self.storage_manager.import_novel_data(**novel_data, filename=filename)
    
    def load_novel_data(self, filename: str) -> dict:
        """加载小说数据"""
        return self.storage_manager.load_novel_file(filename)
    
    def save_chapter_content(self, filename: str, index: int, content: str) -> bool:
        """保存章节内容"""
        return self.storage_manager.save_chapter(filename, index, content)
    
    def create_novel_file(self, book_name: str, filename: str = None) -> str:
        """创建小说文件"""
        return self.storage_manager.create_novel_file(book_name, filename)
    
    def get_novels_list(self) -> list:
        """获取小说列表"""
        return self.storage_manager.get_novels_list()
    
    def get_novel_info(self, filename: str) -> dict:
        """获取小说信息"""
        return self.storage_manager.get_novel_info(filename)
