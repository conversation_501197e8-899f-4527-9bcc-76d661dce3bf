import os
import json
import re
from typing import Optional

class StorageKit:
    def __init__(self, base_dir=None):
        if base_dir is None:
            # 使用默认的小说保存目录
            try:
                from src.browser_kit.config_manager import ConfigManager
                config_manager = ConfigManager()
                base_dir = config_manager.get_novels_dir()
            except:
                base_dir = './novels'
        self.base_dir = base_dir
        # 确保目录存在
        os.makedirs(self.base_dir, exist_ok=True)

    def _sanitize_filename(self, name: str) -> str:
        # 只保留中英文、数字、下划线，其他替换为下划线
        return re.sub(r'[^\w\u4e00-\u9fa5]', '_', name)

    def create_novel_file(self, book_name: str, filename: Optional[str] = None) -> str:
        """
        创建一个初始json文件，只包含book_name等基础字段。
        返回实际创建的文件名。
        """
        if not filename:
            safe_name = self._sanitize_filename(book_name)
            filename = f"{safe_name}.json"
        file_path = os.path.join(self.base_dir, filename)
        data = {
            "book_name": book_name
        }
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return filename

    def import_novel_data(
        self,
        novel_id: str,
        book_name: str,
        cover_img: str,
        author: str,
        update_time: str,
        latest_chapter: str,
        taglist: list,
        categorytags: dict,
        intro: str,
        work_intro: str,
        chapters: list,
        filename: Optional[str] = None
    ) -> None:
        """
        向指定json文件写入完整数据（覆盖写入）。
        :param filename: 文件名（如 '重启人生.json'），如果未传则自动用book_name生成
        其他参数参考重启人生.json结构
        """
        # 自动将章节index重排为从1开始递增
        for i, chapter in enumerate(chapters, start=1):
            chapter['index'] = i
        data = {
            "novel_id": novel_id,
            "book_name": book_name,
            "cover_img": cover_img,
            "author": author,
            "update_time": update_time,
            "latest_chapter": latest_chapter,
            "taglist": taglist,
            "categorytags": categorytags,
            "intro": intro,
            "work_intro": work_intro,
            "chapters": chapters
        }
        if not filename:
            safe_name = self._sanitize_filename(book_name)
            filename = f"{safe_name}.json"
        file_path = os.path.join(self.base_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)

    def save_chapter(self, filename: str, index: int, content: str) -> bool:
        """
        保存章节内容到指定文件
        
        Args:
            filename: 小说文件名
            index: 章节索引
            content: 章节内容
            
        Returns:
            是否保存成功
        """
        try:
            file_path = os.path.join(self.base_dir, filename)
            
            # 如果文件不存在，创建基础结构
            if not os.path.exists(file_path):
                data = {
                    "book_name": os.path.splitext(filename)[0],
                    "chapters": []
                }
            else:
                # 读取现有数据
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if "chapters" not in data:
                    data["chapters"] = []
            
            # 查找或创建章节
            chapter_found = False
            for chapter in data["chapters"]:
                if chapter.get("index") == index:
                    chapter["content"] = content
                    chapter_found = True
                    break
            
            if not chapter_found:
                # 添加新章节
                new_chapter = {
                    "index": index,
                    "content": content,
                    "title": f"第{index}章",
                    "url": ""
                }
                data["chapters"].append(new_chapter)
                # 按index排序
                data["chapters"].sort(key=lambda x: x.get("index", 0))
            
            # 保存文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"保存章节失败: {e}")
            return False