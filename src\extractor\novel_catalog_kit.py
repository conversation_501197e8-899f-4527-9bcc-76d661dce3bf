"""
小说目录获取组件 - 完全基于原始novel_catalog_kit.py
整合所有目录解析功能到单一组件中
"""

import os
import json
import re
import time
import random
from typing import Optional
import requests
from bs4 import BeautifulSoup
import logging

# 可选导入，提供降级方案
try:
    import httpx
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False
    print("警告: httpx 未安装，将使用 requests 作为替代")

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    print("警告: PyYAML 未安装，将使用默认配置")

# 导入存储组件
try:
    from ..storage.storage_manager import StorageManager
except ImportError:
    import sys
    from pathlib import Path
    src_path = Path(__file__).parent.parent
    sys.path.insert(0, str(src_path))
    from storage.storage_manager import StorageManager

logger = logging.getLogger("novel_catalog")


class NovelCatalogKit:
    """
    小说目录获取组件 - 完全基于原始novel_catalog_kit.py
    整合目录解析、章节提取、数据存储等功能
    """
    
    def __init__(self, base_dir=None):
        """初始化小说目录组件"""
        self.storage = StorageManager(base_dir=base_dir)
        logger.info("小说目录组件初始化完成")

    def get_catalog_sync(self, url: str, browser=None, filename: Optional[str] = None) -> dict:
        """
        同步获取小说目录（适配统一 Playwright 架构）
        完全基于原始novel_catalog_kit.py的实现

        Args:
            url: 小说URL
            browser: Playwright 页面对象或浏览器对象
            filename: 文件名

        Returns:
            包含小说信息的字典
        """
        html = None
        if browser is not None:
            # 支持 Playwright 页面对象
            if hasattr(browser, 'content'):
                # 这是 Playwright 页面对象
                import asyncio
                try:
                    # 尝试获取页面内容
                    html = asyncio.run(browser.content()) if asyncio.iscoroutinefunction(browser.content) else browser.content()
                except:
                    html = None
            elif hasattr(browser, 'html'):
                # 兼容 DrissionPage 格式
                html = browser.html
            elif hasattr(browser, 'page') and hasattr(browser.page, 'content'):
                # 浏览器对象包含页面
                import asyncio
                try:
                    html = asyncio.run(browser.page.content()) if asyncio.iscoroutinefunction(browser.page.content) else browser.page.content()
                except:
                    html = None
        
        if not html:
            # 使用HTTP请求获取页面
            try:
                # 优先使用httpx，降级到requests
                if HTTPX_AVAILABLE:
                    resp = httpx.get(url)
                    html = resp.text
                else:
                    resp = requests.get(url)
                    resp.encoding = resp.apparent_encoding
                    html = resp.text

                # 延时配置
                try:
                    config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'config.yaml')
                    if YAML_AVAILABLE and os.path.exists(config_path):
                        with open(config_path, 'r', encoding='utf-8') as f:
                            config = yaml.safe_load(f)
                        interval_min = config.get('interval_min', 1)
                        interval_max = config.get('interval_max', 3)
                    else:
                        interval_min, interval_max = 1, 3
                except:
                    interval_min, interval_max = 1, 3

                time.sleep(random.uniform(interval_min, interval_max))

            except Exception as e:
                logger.error(f"获取页面失败: {str(e)}")
                return {'error': f'获取页面失败: {str(e)}'}
        
        soup = BeautifulSoup(html, 'html.parser')

        # 封面
        cover_img_tag = soup.select_one('#bookImg img')
        cover_img = cover_img_tag['src'] if cover_img_tag and cover_img_tag.has_attr('src') else ''
        
        # 书名
        book_name_tag = soup.select_one('#bookName')
        book_name = book_name_tag.text.strip() if book_name_tag else ''
        
        # 作者
        author_tag = soup.select_one('.author')
        author = author_tag.text.replace('作者:', '').strip() if author_tag else ''
        
        # 更新时间
        update_time_tag = soup.select_one('.update-time')
        update_time = update_time_tag.text.replace('更新时间:', '').strip() if update_time_tag else ''
        
        # 最新章节
        latest_chapter_tag = soup.select_one('.book-latest-chapter')
        latest_chapter = latest_chapter_tag.text.replace('最新章节:', '').strip() if latest_chapter_tag else ''
        
        # taglist（属性/分类/标签）
        taglist_tags = soup.select('.book-attribute span, .book-attribute a')
        taglist = [tag.text.strip() for tag in taglist_tags] if taglist_tags else []
        # 去除点号
        taglist = [t for t in taglist if t != '·']
        # 去重，防止标签重复
        taglist = list(dict.fromkeys(taglist))
        
        # categorytags（每两个标签一组，分割为列表，效果与taglist一致）
        # 注意：原始代码中这里是list，但storage_kit.py中定义为dict，需要转换
        count_tag = soup.select_one('p.count')
        categorytags_list = []
        if count_tag:
            ems = count_tag.find_all('em')
            cites = count_tag.find_all('cite')
            for em, cite in zip(ems, cites):
                categorytags_list.append(em.get_text(strip=True) + cite.get_text(strip=True))
        
        # 保持list格式，与原始novel_catalog_kit.py一致
        categorytags = categorytags_list
        
        # 简介
        intro_tag = soup.select_one('p.intro')
        intro = intro_tag.text.strip() if intro_tag else ''
        
        # 作品简介
        work_intro_tag = soup.select_one('#book-intro-detail')
        work_intro = ''
        if work_intro_tag:
            # 保留换行符，将<br>替换为\n
            work_intro = work_intro_tag.decode_contents().replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
            # 去除HTML标签
            from bs4 import BeautifulSoup as BS
            work_intro = BS(work_intro, 'html.parser').get_text()
            work_intro = work_intro.strip()
        
        # 小说ID
        novel_id = url.rstrip('/').split('/')[-1]

        # 解析卷和章节
        volumes = []
        catalog_volumes = soup.select('.catalog-volume')
        for vol in catalog_volumes:
            # 卷名
            volume_name_tag = vol.select_one('.volume-name')
            volume_name = volume_name_tag.get_text(strip=True) if volume_name_tag else ''
            # 章节列表
            chapters = []
            for chapter_li in vol.select('.volume-chapters .chapter-item'):
                chapter_a = chapter_li.select_one('.chapter-name')
                chapter_name = chapter_a.get_text(strip=True) if chapter_a else ''
                chapter_url = ''
                if chapter_a and chapter_a.has_attr('href'):
                    href_val = chapter_a['href']
                    # 处理AttributeValueList或ResultSet
                    if isinstance(href_val, (list, tuple)):
                        href_val = href_val[0] if href_val else ''
                    chapter_url = str(href_val)
                # 补全url
                if chapter_url and isinstance(chapter_url, str) and chapter_url.startswith('//'):
                    chapter_url = 'https:' + chapter_url
                chapters.append({
                    'chapter_name': chapter_name,
                    'url': chapter_url
                })
            volumes.append({
                'volume_name': volume_name,
                'chapters': chapters
            })
        
        # 兼容原有chapters字段（所有章节平铺）
        all_chapters = []
        idx = 1
        for vol in volumes:
            for chapter in vol['chapters']:
                chapter_flat = chapter.copy()
                # index放在最前面
                chapter_flat = {
                    'index': idx,
                    'chapter_name': chapter_flat.get('chapter_name', ''),
                    'url': chapter_flat.get('url', ''),
                    'volume_name': vol['volume_name'],
                    'content': ''  # 初始化content字段
                }
                all_chapters.append(chapter_flat)
                idx += 1

        data = {
            'novel_id': novel_id,
            'book_name': book_name,
            'cover_img': cover_img,
            'author': author,
            'update_time': update_time,
            'latest_chapter': latest_chapter,
            'taglist': taglist,
            'categorytags': categorytags,  # 现在是dict格式
            'intro': intro,
            'work_intro': work_intro,
            'chapters': all_chapters,  # 兼容原有章节平铺
        }
        
        # 保存数据
        try:
            self.storage.import_novel_data(**data, filename=filename)
            logger.info(f"小说信息保存成功: {book_name}")
        except Exception as e:
            logger.error(f"保存小说信息失败: {str(e)}")
            data['save_error'] = str(e)
        
        # 返回data时可以包含volumes
        data['volumes'] = volumes
        return data

    def get_chapter_content(self, chapter_url: str, filename: str, index: int) -> None:
        """
        获取章节内容 - 完全基于原始novel_catalog_kit.py的实现

        Args:
            chapter_url: 章节URL
            filename: 小说文件名
            index: 章节索引
        """
        try:
            # 读取现有数据
            file_path = os.path.join(self.storage.base_dir, filename)
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 获取章节页面
            resp = requests.get(chapter_url)

            # 延时配置
            try:
                config_path = os.path.join(os.path.dirname(__file__), '..', '..', 'config', 'config.yaml')
                if YAML_AVAILABLE and os.path.exists(config_path):
                    with open(config_path, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f)
                    interval_min = config.get('interval_min', 1)
                    interval_max = config.get('interval_max', 3)
                else:
                    interval_min, interval_max = 1, 3
            except:
                interval_min, interval_max = 1, 3

            time.sleep(random.uniform(interval_min, interval_max))
            resp.encoding = resp.apparent_encoding
            soup = BeautifulSoup(resp.text, 'html.parser')

            # 提取章节内容
            content = ''
            content_tag = soup.select_one('.read-content')
            if content_tag:
                content = '\n'.join([p.text.strip() for p in content_tag.find_all('p')])

            # 更新章节内容
            if 1 <= index <= len(data['chapters']):
                data['chapters'][index-1]['content'] = content

            # 保存更新后的数据
            self.storage.import_novel_data(
                novel_id=data['novel_id'],
                book_name=data['book_name'],
                cover_img=data['cover_img'],
                author=data['author'],
                update_time=data['update_time'],
                latest_chapter=data['latest_chapter'],
                taglist=data.get('taglist', []),
                categorytags=data['categorytags'],
                intro=data['intro'],
                work_intro=data.get('work_intro', ''),
                chapters=data['chapters'],
                filename=filename
            )

            logger.info(f"章节内容获取成功: {filename} - 第{index}章")

        except Exception as e:
            logger.error(f"获取章节内容失败: {str(e)}")

    async def get_catalog(self, url: str, browser=None, filename: Optional[str] = None) -> dict:
        """保留异步版本以兼容旧代码"""
        return self.get_catalog_sync(url, browser, filename)

    # ==================== 便利方法 ====================

    def get_novels_list(self):
        """获取小说列表"""
        return self.storage.get_novels_list()

    def search_novels(self, keyword: str):
        """搜索小说"""
        return self.storage.search_novels(keyword)

    def get_novel_info(self, filename: str):
        """获取小说信息"""
        return self.storage.get_novel_info(filename)

    def delete_novel(self, filename: str):
        """删除小说"""
        return self.storage.delete_novel(filename)

    def get_storage_stats(self):
        """获取存储统计"""
        return self.storage.get_storage_stats()
